<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>胶囊翻译</title>
    <link rel="stylesheet" href="capsule-window.css">
</head>
<body>
    <div id="capsule-container" class="capsule-container">
        <!-- 顶部拖拽区域 -->
        <div class="drag-area" id="drag-area">
            <div class="window-controls">
                <button class="close-btn" id="close-btn" title="关闭">
                    <svg width="12" height="12" viewBox="0 0 12 12">
                        <path d="M1 1l10 10M11 1L1 11" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                    </svg>
                </button>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="content-area">
            <!-- 输入区域 -->
            <div class="input-section">
                <div class="input-container">
                    <textarea 
                        id="input-text" 
                        class="input-text" 
                        placeholder="输入要翻译的文本..."
                        rows="3"
                    ></textarea>
                    <div class="input-actions">
                        <button class="action-btn clear-btn" id="clear-btn" title="清空">
                            <svg width="16" height="16" viewBox="0 0 16 16">
                                <path d="M2 4h12M5.5 4V2.5a1 1 0 011-1h3a1 1 0 011 1V4M7 7v6M9 7v6M4 4v9a1 1 0 001 1h6a1 1 0 001-1V4" 
                                      stroke="currentColor" stroke-width="1.2" fill="none" stroke-linecap="round"/>
                            </svg>
                        </button>
                        <button class="action-btn translate-btn" id="translate-btn" title="翻译">
                            <svg width="16" height="16" viewBox="0 0 16 16">
                                <path d="M8 2v12M2 8h12" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 分隔线 -->
            <div class="divider"></div>

            <!-- 输出区域 -->
            <div class="output-section">
                <div class="output-container">
                    <div class="output-text" id="output-text">
                        <div class="placeholder-text">翻译结果将显示在这里...</div>
                    </div>
                    <div class="output-actions">
                        <button class="action-btn copy-btn" id="copy-btn" title="复制" style="display: none;">
                            <svg width="16" height="16" viewBox="0 0 16 16">
                                <path d="M4 2h6a2 2 0 012 2v6M4 6h6a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V8a2 2 0 012-2z" 
                                      stroke="currentColor" stroke-width="1.2" fill="none" stroke-linecap="round"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部状态栏 -->
        <div class="status-bar">
            <div class="status-text" id="status-text">就绪</div>
            <div class="language-selector">
                <select id="source-lang" class="lang-select">
                    <option value="auto">自动检测</option>
                    <option value="zh">中文</option>
                    <option value="en">英文</option>
                    <option value="ja">日文</option>
                    <option value="ko">韩文</option>
                </select>
                <div class="arrow-icon">
                    <svg width="16" height="16" viewBox="0 0 16 16">
                        <path d="M3 6l5 5 5-5" stroke="currentColor" stroke-width="1.5" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
                <select id="target-lang" class="lang-select">
                    <option value="zh">中文</option>
                    <option value="en">英文</option>
                    <option value="ja">日文</option>
                    <option value="ko">韩文</option>
                </select>
            </div>
        </div>

        <!-- 加载指示器 -->
        <div class="loading-overlay" id="loading-overlay" style="display: none;">
            <div class="loading-spinner">
                <div class="spinner"></div>
                <div class="loading-text">翻译中...</div>
            </div>
        </div>
    </div>

    <script src="capsule-window.js"></script>
</body>
</html>
