// 胶囊翻译小窗 JavaScript 逻辑

class CapsuleTranslator {
    constructor() {
        this.isTranslating = false;
        this.currentWindow = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.setupWindow();

        setTimeout(() => {
            this.ensureRoundedCorners();
            this.applyOptimalTransparency();
        }, 200);
        this.updateStatus('就绪');


        this.startRoundedCornersWatcher();
    }

    bindEvents() {
        // 关闭按钮
        document.getElementById('close-btn').addEventListener('click', () => {
            this.closeWindow();
        });

        // 清空按钮
        document.getElementById('clear-btn').addEventListener('click', () => {
            this.clearInput();
        });

        // 翻译按钮
        document.getElementById('translate-btn').addEventListener('click', () => {
            this.translate();
        });

        // 复制按钮
        document.getElementById('copy-btn').addEventListener('click', () => {
            this.copyResult();
        });

        // 输入框事件
        const inputText = document.getElementById('input-text');
        inputText.addEventListener('input', () => {
            this.onInputChange();
        });

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            this.handleKeydown(e);
        });

        // 语言选择器
        document.getElementById('source-lang').addEventListener('change', () => {
            this.onLanguageChange();
        });

        document.getElementById('target-lang').addEventListener('change', () => {
            this.onLanguageChange();
        });

        // 窗口焦点事件
        window.addEventListener('focus', () => {
            this.onWindowFocus();
        });

        window.addEventListener('blur', () => {
            this.onWindowBlur();
        });

        // 监听主题变化
        if (window.matchMedia) {
            const darkModeQuery = window.matchMedia('(prefers-color-scheme: dark)');
            darkModeQuery.addEventListener('change', () => {
                setTimeout(() => {
                    this.applyOptimalTransparency();
                }, 100);
            });
        }
    }

    setupWindow() {
        // 获取当前窗口引用（如果在 uTools 环境中）
        if (typeof utools !== 'undefined') {
            this.currentWindow = utools.getCurrentWindow?.();
        }

        // 设置窗口初始状态
        this.ensureRoundedCorners();
    }

    // 启动圆角监视器 - 最小化版本
    startRoundedCornersWatcher() {

        this.roundedCornersInterval = setInterval(() => {
            this.ensureRoundedCorners();
        }, 30000);


        window.addEventListener('resize', () => {
            setTimeout(() => {
                this.ensureRoundedCorners();
            }, 300);
        });
    }

    closeWindow() {
        try {
            // 清理监视器
            if (this.roundedCornersInterval) {
                clearInterval(this.roundedCornersInterval);
            }

            if (typeof utools !== 'undefined' && this.currentWindow) {
                this.currentWindow.close();
            } else if (window.close) {
                window.close();
            }
        } catch (error) {
            console.error('关闭窗口失败:', error);
        }
    }

    clearInput() {
        const inputText = document.getElementById('input-text');
        const outputText = document.getElementById('output-text');
        
        inputText.value = '';
        outputText.innerHTML = '<div class="placeholder-text">翻译结果将显示在这里...</div>';
        
        this.hideCopyButton();
        this.updateStatus('已清空');
        
        // 聚焦到输入框
        inputText.focus();
    }

    async translate() {
        const inputText = document.getElementById('input-text');
        const text = inputText.value.trim();

        if (!text) {
            this.updateStatus('请输入要翻译的文本');
            return;
        }

        if (this.isTranslating) {
            return;
        }

        this.isTranslating = true;
        this.showLoading();
        this.updateStatus('翻译中...');

        try {
            // TODO: 集成实际翻译服务
            this.showError('翻译功能开发中，敬请期待');
        } catch (error) {
            this.showError('翻译失败，请稍后重试');
        } finally {
            this.isTranslating = false;
            this.hideLoading();
        }
    }



    copyResult() {
        const outputText = document.getElementById('output-text');
        const text = outputText.textContent;

        if (!text || text.includes('翻译结果将显示在这里')) {
            return;
        }

        try {
            // 尝试使用 uTools API 复制
            if (typeof utools !== 'undefined' && utools.copyText) {
                utools.copyText(text);
                this.updateStatus('已复制到剪贴板');
            } else {
                // 备用复制方法
                navigator.clipboard.writeText(text).then(() => {
                    this.updateStatus('已复制到剪贴板');
                }).catch(() => {
                    this.updateStatus('复制失败');
                });
            }
        } catch (error) {
            console.error('复制失败:', error);
            this.updateStatus('复制失败');
        }
    }

    onInputChange() {
        const inputText = document.getElementById('input-text');
        const hasText = inputText.value.trim().length > 0;
        
        if (hasText) {
            this.updateStatus('准备翻译');
        } else {
            this.updateStatus('就绪');
        }
    }

    handleKeydown(e) {
        // Ctrl/Cmd + Enter 翻译
        if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
            e.preventDefault();
            this.translate();
        }
        
        // Escape 关闭窗口
        if (e.key === 'Escape') {
            e.preventDefault();
            this.closeWindow();
        }
        
        // Ctrl/Cmd + L 清空
        if ((e.ctrlKey || e.metaKey) && e.key === 'l') {
            e.preventDefault();
            this.clearInput();
        }
    }

    onLanguageChange() {
        this.updateStatus('语言设置已更改');
    }

    onWindowFocus() {
        // 检测焦点来源并应用最佳透明效果
        this.applyOptimalTransparency();
        this.updateStatus('窗口已聚焦');
    }

    onWindowBlur() {
        // 焦点丢失时保持当前状态
        this.updateStatus('窗口失去焦点');
    }

    // 应用最佳透明效果
    applyOptimalTransparency() {
        const container = document.querySelector('.capsule-container');
        if (container) {
            // 强制应用最佳透明效果
            container.style.background = 'rgba(255, 255, 255, 0.94)';
            container.style.backdropFilter = 'blur(8px)';
            container.style.webkitBackdropFilter = 'blur(8px)';

            // 深色主题适配
            if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
                container.style.background = 'rgba(30, 30, 30, 0.94)';
            }
        }
    }



    // 确保圆角效果始终存在 - 轻量级检查
    ensureRoundedCorners() {
        const container = document.querySelector('.capsule-container');

        if (container) {
            // 只检查和修复关键的圆角样式，避免过度干预
            const currentRadius = getComputedStyle(container).borderRadius;
            if (!currentRadius || currentRadius === '0px' || currentRadius === 'none') {
                container.style.borderRadius = '12px';
                container.style.clipPath = 'inset(0 round 12px)';
                container.style.webkitClipPath = 'inset(0 round 12px)';
                container.style.overflow = 'hidden';
            }
        }
    }

    showLoading() {
        const loadingOverlay = document.getElementById('loading-overlay');
        loadingOverlay.style.display = 'flex';
    }

    hideLoading() {
        const loadingOverlay = document.getElementById('loading-overlay');
        loadingOverlay.style.display = 'none';
    }

    showCopyButton() {
        const copyBtn = document.getElementById('copy-btn');
        copyBtn.style.display = 'flex';
    }

    hideCopyButton() {
        const copyBtn = document.getElementById('copy-btn');
        copyBtn.style.display = 'none';
    }

    showError(message) {
        const outputText = document.getElementById('output-text');
        outputText.innerHTML = `<div style="color: #ff3b30;">${message}</div>`;
        this.updateStatus('错误');
    }

    updateStatus(message) {
        const statusText = document.getElementById('status-text');
        statusText.textContent = message;
        
        // 3秒后恢复默认状态
        if (message !== '就绪') {
            setTimeout(() => {
                if (statusText.textContent === message) {
                    statusText.textContent = '就绪';
                }
            }, 3000);
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    window.capsuleTranslator = new CapsuleTranslator();
});

// 如果在 uTools 环境中，监听父窗口消息
if (typeof utools !== 'undefined') {
    // 可以在这里添加与主窗口的通信逻辑
}
