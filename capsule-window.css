/* 胶囊翻译小窗样式 */

/* 确保整个窗口都有圆角效果 */
:root {
    --capsule-radius: 12px;
}



/* 容器圆角设置 */
.capsule-container {
    border-radius: var(--capsule-radius);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    background: transparent;
    overflow: hidden;
    width: 100%;
    height: 100%;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    background: transparent;
    overflow: hidden;
    user-select: none;
    -webkit-user-select: none;
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
}

.capsule-container {
    width: 100%;
    height: 100vh;
    background: rgba(255, 255, 255, 0.96);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);

    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1),
                0 2px 8px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
    margin: 0;
    padding: 0;
    /* 圆角裁剪 */
    -webkit-clip-path: inset(0 round var(--capsule-radius));
    clip-path: inset(0 round var(--capsule-radius));
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
    .capsule-container {
        background: rgba(30, 30, 30, 0.96);
        border: 1px solid rgba(255, 255, 255, 0.1);
        color: #ffffff;
    }
}

/* 拖拽区域 */
.drag-area {
    height: 40px;
    background: transparent;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 0 12px;
    -webkit-app-region: drag;
    cursor: move;
}

.window-controls {
    -webkit-app-region: no-drag;
}

.close-btn {
    width: 24px;
    height: 24px;
    border: none;
    background: rgba(255, 59, 48, 0.1);
    border-radius: 50%;
    color: #ff3b30;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.close-btn:hover {
    background: rgba(255, 59, 48, 0.2);
    transform: scale(1.1);
}

/* 内容区域 */
.content-area {
    flex: 1;
    padding: 0 20px;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

/* 输入区域 */
.input-section {
    flex: 1;
}

.input-container {
    position: relative;
    height: 100%;
}

.input-text {
    width: 100%;
    height: 100%;
    border: none;
    background: rgba(0, 0, 0, 0.03);
    border-radius: 12px;
    padding: 12px 50px 12px 12px;
    font-size: 14px;
    line-height: 1.4;
    resize: none;
    outline: none;
    font-family: inherit;
    transition: all 0.2s ease;
}

.input-text:focus {
    background: rgba(0, 0, 0, 0.05);
    box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.2);
}

@media (prefers-color-scheme: dark) {
    .input-text {
        background: rgba(255, 255, 255, 0.05);
        color: #ffffff;
    }
    
    .input-text:focus {
        background: rgba(255, 255, 255, 0.08);
    }
    
    .input-text::placeholder {
        color: rgba(255, 255, 255, 0.5);
    }
}

.input-actions {
    position: absolute;
    right: 8px;
    top: 8px;
    display: flex;
    gap: 4px;
}

/* 分隔线 */
.divider {
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent);
    margin: 0 -20px;
}

@media (prefers-color-scheme: dark) {
    .divider {
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    }
}

/* 输出区域 */
.output-section {
    flex: 1;
}

.output-container {
    position: relative;
    height: 100%;
}

.output-text {
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.02);
    border-radius: 12px;
    padding: 12px 50px 12px 12px;
    font-size: 14px;
    line-height: 1.4;
    overflow-y: auto;
    user-select: text;
    -webkit-user-select: text;
}

@media (prefers-color-scheme: dark) {
    .output-text {
        background: rgba(255, 255, 255, 0.03);
        color: #ffffff;
    }
}

.placeholder-text {
    color: rgba(0, 0, 0, 0.4);
    font-style: italic;
}

@media (prefers-color-scheme: dark) {
    .placeholder-text {
        color: rgba(255, 255, 255, 0.4);
    }
}

.output-actions {
    position: absolute;
    right: 8px;
    top: 8px;
}

/* 按钮样式 */
.action-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    color: rgba(0, 0, 0, 0.6);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.action-btn:hover {
    background: rgba(0, 0, 0, 0.1);
    color: rgba(0, 0, 0, 0.8);
    transform: scale(1.05);
}

.translate-btn {
    background: rgba(0, 122, 255, 0.1);
    color: #007aff;
}

.translate-btn:hover {
    background: rgba(0, 122, 255, 0.2);
}

@media (prefers-color-scheme: dark) {
    .action-btn {
        background: rgba(255, 255, 255, 0.08);
        color: rgba(255, 255, 255, 0.6);
    }
    
    .action-btn:hover {
        background: rgba(255, 255, 255, 0.12);
        color: rgba(255, 255, 255, 0.8);
    }
}

/* 状态栏 */
.status-bar {
    height: 50px;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: rgba(0, 0, 0, 0.02);
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

@media (prefers-color-scheme: dark) {
    .status-bar {
        background: rgba(255, 255, 255, 0.03);
        border-top: 1px solid rgba(255, 255, 255, 0.05);
    }
}

.status-text {
    font-size: 12px;
    color: rgba(0, 0, 0, 0.5);
}

@media (prefers-color-scheme: dark) {
    .status-text {
        color: rgba(255, 255, 255, 0.5);
    }
}

.language-selector {
    display: flex;
    align-items: center;
    gap: 8px;
}

.lang-select {
    border: none;
    background: transparent;
    font-size: 12px;
    color: inherit;
    cursor: pointer;
    outline: none;
}

.arrow-icon {
    color: rgba(0, 0, 0, 0.3);
}

@media (prefers-color-scheme: dark) {
    .arrow-icon {
        color: rgba(255, 255, 255, 0.3);
    }
}

/* 加载指示器 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--capsule-radius);
}

@media (prefers-color-scheme: dark) {
    .loading-overlay {
        background: rgba(30, 30, 30, 0.8);
    }
}

.loading-spinner {
    text-align: center;
}

.spinner {
    width: 24px;
    height: 24px;
    border: 2px solid rgba(0, 122, 255, 0.2);
    border-top: 2px solid #007aff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 8px;
}

.loading-text {
    font-size: 12px;
    color: rgba(0, 0, 0, 0.6);
}

@media (prefers-color-scheme: dark) {
    .loading-text {
        color: rgba(255, 255, 255, 0.6);
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 滚动条样式 */
.output-text::-webkit-scrollbar {
    width: 4px;
}

.output-text::-webkit-scrollbar-track {
    background: transparent;
}

.output-text::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 2px;
}

@media (prefers-color-scheme: dark) {
    .output-text::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.2);
    }
}
